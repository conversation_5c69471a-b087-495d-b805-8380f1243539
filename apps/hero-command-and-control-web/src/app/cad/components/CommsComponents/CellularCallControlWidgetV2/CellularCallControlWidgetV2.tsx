import { useAssociatedAssetQuery } from '@/app/apis/custom/auth/GetAssociatedAsset';
import { useAllContactsInContactBook } from '@/app/apis/services/orgs/hooks';
import { useCallContext } from '@/app/contexts/Call/CallContext';
import { useCallAudioNotifications } from '@/app/contexts/Call/useCallAudioNotifications';
import { Button } from '@/design-system/components/Button';
import { colors } from "@/design-system/tokens/colors";
import { spacing } from "@/design-system/tokens/spacing";
import { typography } from "@/design-system/tokens/typography";
import AddIcCallIcon from "@mui/icons-material/AddIcCall";
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import KeyboardArrowUpIcon from "@mui/icons-material/KeyboardArrowUp";
import SettingsIcon from '@mui/icons-material/Settings';
import { Box, Collapse, Tab, Tabs, Typography } from "@mui/material";
import { QueuedCall } from "proto/hero/communications/v1/conversation_pb";
import { memo, useCallback, useEffect, useRef, useState } from "react";
import { ActiveCall } from "../ActiveCallComponent";
import { CallForwardingPanel } from '../CallForwardingPanel';
import { IncomingCallListItem } from '../IncomingCallListItem';
import { ManualDialerOverlay } from '../ManualDialerOverlay';
import { OnHoldCallListItem } from '../OnHoldCallListItem';
import { SHARED_STYLES } from './CellularCallControlWidgetV2.styles';


interface CellularCallControlWidgetV2Props {
  activeCall: QueuedCall | null;
  holdCall: QueuedCall | null;
  isMuted?: boolean;
  isConnecting?: boolean;
  onEndCall?: () => void;
  onHoldToggle?: () => void;
  onDial?: (phoneNumber: string) => void;
  onToggleMute?: () => void;
  onSwitchCall?: () => void;
}

interface TabLabelProps {
  label: string;
  count: number;
  isSelected: boolean;
}

// Memoize TabLabel component
const TabLabel = memo(({ label, count, isSelected }: TabLabelProps) => {
  return (
    <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
      <Typography
        sx={{
          ...typography.styles.body3,
          color: colors.grey[600],
          fontWeight: isSelected ? 500 : 400,
        }}
      >
        {label}
      </Typography>

      <Box
        sx={{
          minWidth: 20,
          height: 20,
          borderRadius: 20,
          backgroundColor: isSelected ? colors.blue[100] : colors.grey[100],
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          padding: "2px 6px",
        }}
      >
        <Typography sx={{
          ...typography.styles.tag1,
          color: isSelected ? colors.blue[700] : colors.grey[700],
        }}>
          {count}
        </Typography>
      </Box>
    </Box>
  );
});

TabLabel.displayName = 'TabLabel';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

// =============================================
// Component: TabPanel
// Purpose: MUI TabPanel wrapper for content switching
// =============================================
const TabPanel: React.FC<TabPanelProps> = ({ children, value, index, ...other }) => (
  <div
    role="tabpanel"
    hidden={value !== index}
    id={`call-tabpanel-${index}`}
    aria-labelledby={`call-tab-${index}`}
    {...other}
  >
    {value === index && (
      <Box sx={{
        maxHeight: 'calc(435px - 180px)',
        overflowY: 'auto',
        height: '100%',
        width: '100%',
      }}>
        {children}
      </Box>
    )}
  </div>
);

TabPanel.displayName = 'TabPanel';

// =============================================
// Main Component: CellularCallControlWidgetV2
// Purpose: Handles call control UI with expandable interface
// =============================================
export default function CellularCallControlWidgetV2({
  activeCall,
  isMuted = false,
  isConnecting = false,
  onEndCall,
  onHoldToggle,
  onDial,
  onToggleMute,
}: CellularCallControlWidgetV2Props) {
  // State Management
  const [isExpanded, setIsExpanded] = useState(false);
  const [tabValue, setTabValue] = useState(0);
  const prevInboundCountRef = useRef<number>(0); // Ref to store previous count
  const [isDialerOpen, setIsDialerOpen] = useState(false);
  const [isDialerActive, setIsDialerActive] = useState(false);
  const [dialerAnchorEl, setDialerAnchorEl] = useState<HTMLElement | null>(null);
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const prevActiveCallRef = useRef<QueuedCall | null>(null);
  const prevConnectingRef = useRef<boolean>(false);

  // Consume CallContext
  const {
    heldCalls: heldCallsResponse,
    resumeSpecificCall,
    queueStatus,
    acceptNextCall,
    dequeueSpecificCall,
    isAcceptingCall,
    callStatus,
    currentActiveCall,
  } = useCallContext();

  // Audio notifications for incoming calls
  const { playNotification, stopAllNotifications } = useCallAudioNotifications();

  // Get current dispatcher asset to extract org ID
  const { data: dispatcherAsset } = useAssociatedAssetQuery();
  
  // Fetch organization contacts using enhanced hook
  const { data: allContactsData} = useAllContactsInContactBook(
    dispatcherAsset?.orgId || 0,
    undefined, // searchQuery (not needed for initial load)
    {
      enabled: dispatcherAsset?.orgId !== undefined && dispatcherAsset?.orgId !== null,
    }
  );

  const contacts = allContactsData?.contacts?.map((contact) => ({
    name: contact.name || '',
    phone: contact.phone || '',
  })) || [];

  // Extract the array from the response object, default to empty array
  const heldCalls = heldCallsResponse?.heldCalls || [];

  // Calculate counts dynamically
  const onHoldCount = heldCalls.length;
  const inboundCount = queueStatus?.waitingCalls?.length || 0;

  // Effect to handle auto-expansion on mount if active or held calls exist
  useEffect(() => {
    if (activeCall || heldCalls.length > 0) {
      console.log('[WidgetV2] Initial mount with active or held call. Expanding widget.');
      setIsExpanded(true);
    }
    // Run only once on mount
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Effect to handle auto-expansion and tab switching on new incoming call
  useEffect(() => {
    const currentInboundCount = queueStatus?.waitingCalls?.length || 0;
    const previousInboundCount = prevInboundCountRef.current;

    // Trigger only when count increases from 0 to positive
    if (currentInboundCount > 0 && previousInboundCount === 0) {
      console.log('[WidgetV2] New incoming call detected. Expanding and switching to Inbound tab.');
      setIsExpanded(true);
      setTabValue(1);
    }

    // Audio notification: Trigger for ANY queue increase (0→1, 1→2, 2→3, etc.)
    if (currentInboundCount > previousInboundCount) {
      try {
        // Play notification based on this dispatcher's current state
        playNotification(!!currentActiveCall);
        console.log('[WidgetV2] Audio notification triggered for queue increase', {
          previousCount: previousInboundCount,
          currentCount: currentInboundCount,
          isDispatcherActive: !!currentActiveCall,
          volumeLevel: currentActiveCall ? 'subtle' : 'loud'
        });
      } catch (audioError) {
        console.warn('[WidgetV2] Audio notification failed:', audioError);
        // Don't propagate error - audio is non-critical to widget functionality
      }
    }

    // Stop audio on queue decrease (handles caller hangups, call holds, etc.)
    if (currentInboundCount < previousInboundCount) {
      try {
        stopAllNotifications();
        console.log('[WidgetV2] Audio notification stopped due to queue decrease', {
          previousCount: previousInboundCount,
          currentCount: currentInboundCount,
          reason: 'caller_hangup_or_transfer'
        });
      } catch (audioError) {
        console.warn('[WidgetV2] Failed to stop audio notifications:', audioError);
      }
    }

    // Update the ref with the current count for the next render
    prevInboundCountRef.current = currentInboundCount;

  }, [queueStatus?.waitingCalls]); // Depend on the waitingCalls array

  // Effect to handle auto-expansion when a call becomes active
  useEffect(() => {
    const prevCall = prevActiveCallRef.current;

    // If we have a new active call and we didn't have one before, expand the widget
    if (activeCall && !prevCall) {
      console.log('[WidgetV2] New active call detected. Expanding widget.');
      setIsExpanded(true);
    }

    // Update the ref with the current active call for the next render
    prevActiveCallRef.current = activeCall;
  }, [activeCall]);

  // Effect to handle auto-expansion when a call is connecting
  useEffect(() => {
    const prevConnecting = prevConnectingRef.current;

    // If a call is now connecting and wasn't before, expand the widget
    if (isConnecting && !prevConnecting) {
      console.log('[WidgetV2] Call is now connecting. Expanding widget.');
      setIsExpanded(true);
    }

    // Update the ref with the current connecting state for the next render
    prevConnectingRef.current = isConnecting;
  }, [isConnecting]);

  // Effect to handle auto-expansion when call status changes to active
  useEffect(() => {
    if (callStatus === 'active') {
      console.log('[WidgetV2] Call status is now active. Expanding widget.');
      setIsExpanded(true);
    }
  }, [callStatus]);

  // Track previous values for comparison
  const prevCallStatusRef = useRef<string | undefined>(undefined);
  const prevOnHoldCountRef = useRef<number>(0);

  // Effect to handle tab switching when calls are placed on hold
  useEffect(() => {
    const incomingCallCount = queueStatus?.waitingCalls?.length || 0;
    const isCallStatusChangedToHeld = callStatus === 'held' && prevCallStatusRef.current !== 'held';
    const isOnHoldCountIncreased = onHoldCount > prevOnHoldCountRef.current;

    // Switch to On Hold tab if either condition is met and there are no incoming calls
    if ((isCallStatusChangedToHeld || isOnHoldCountIncreased) &&
      onHoldCount > 0 &&
      incomingCallCount === 0) {
      console.log('[WidgetV2] Call placed on hold. Switching to On Hold tab.');
      setTabValue(0);
    }

    // Update refs for next comparison
    prevCallStatusRef.current = callStatus;
    prevOnHoldCountRef.current = onHoldCount;
  }, [callStatus, onHoldCount, queueStatus?.waitingCalls]);

  // Event Handlers
  const handleTabChange = useCallback((_: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  }, []);

  const handleExpandToggle = useCallback(() => {
    setIsExpanded(!isExpanded);
  }, [isExpanded]);

  // New handler for switching calls
  const handleSwitchCall = useCallback((call: QueuedCall) => {
    if (call.callSid) {
      console.log(`[WidgetV2] Resuming call: ${call.callSid}`);
      resumeSpecificCall(call);
    } else {
      console.error('[WidgetV2] Cannot switch call: callSid is missing', call);
    }
  }, [resumeSpecificCall]);

  // Handler for accepting the next call from the queue
  const handleAcceptNextCall = useCallback(async () => {
    console.log('[WidgetV2] Attempting to accept next call...');
    try {
      // Stop audio notifications when accepting a call
      stopAllNotifications();
      await acceptNextCall();
      console.log('[WidgetV2] acceptNextCall initiated.');
      // Auto-expand the widget when accepting a call
      setIsExpanded(true);
    } catch (error) {
      console.error('[WidgetV2] Error accepting next call:', error);
    }
  }, [acceptNextCall, stopAllNotifications]);

  // Handler for dequeuing a specific call
  const handleDequeueCall = useCallback((callSid: string) => {
    console.log(`[WidgetV2] Dequeuing call: ${callSid}`);
    // Stop audio notifications when dequeuing a specific call
    stopAllNotifications();
    dequeueSpecificCall(callSid);
  }, [dequeueSpecificCall, stopAllNotifications]);

  // --- Internal Dial Handler --- 
  // This handler is passed down to the ManualDialerOverlay
  // It ensures that the main onDial prop (if provided) is called
  const internalHandleDial = useCallback((phoneNumber: string) => {
    console.log(`[WidgetV2] Dialing: ${phoneNumber}`);
    if (onDial) {
      onDial(phoneNumber);
      // Auto-expand the widget when dialing
      setIsExpanded(true);
    } else {
      console.warn('[WidgetV2] onDial prop not provided.');
    }
    // Close the dialer after initiating the dial
    setIsDialerOpen(false);
    setIsDialerActive(false);
    setDialerAnchorEl(null); // Clear anchor
  }, [onDial]);

  const handleDialerClose = useCallback(() => {
    setIsDialerOpen(false);
    setIsDialerActive(false);
    setDialerAnchorEl(null);
  }, []);

  const handleDialerToggle = useCallback((event: React.MouseEvent<HTMLButtonElement>) => {
    setDialerAnchorEl(event.currentTarget);
    setIsDialerOpen(prev => !prev);
    setIsDialerActive(prev => !prev);
  }, []);

  // Handler for when a call is initiated from the dialer
  const handleCallInitiated = useCallback(() => {
    console.log('[WidgetV2] Call initiated from dialer, expanding widget');
    setIsExpanded(true);
  }, []);

  // Settings handlers
  const handleSettingsToggle = useCallback(() => {
    setIsSettingsOpen(prev => {
      const newSettingsState = !prev;
      
      // Auto-expand widget when opening settings
      if (newSettingsState) {
        setIsExpanded(true);
        console.log('[WidgetV2] Settings opened - expanding widget');
      }
      
      console.log('[WidgetV2] Settings panel toggled:', newSettingsState);
      return newSettingsState;
    });
  }, []);

  const handleSettingsClose = useCallback(() => {
    setIsSettingsOpen(false);
    console.log('[WidgetV2] Settings panel closed');
  }, []);

  // =============================================
  // Render Functions
  // =============================================

  // Header Section: Contains title and control buttons
  const renderHeader = useCallback(() => (
    <Box sx={SHARED_STYLES.header.container}>
      <Typography sx={SHARED_STYLES.header.text}>
        Active Calls
      </Typography>
      <Box sx={{ display: "flex", alignItems: "center", gap: spacing.xs, position: "relative", zIndex: 3 }}>
        {/* Settings Button - leftmost */}
        <Box
          onClick={handleSettingsToggle}
          role="button"
          tabIndex={0}
          aria-label="Open call forwarding settings"
          aria-expanded={isSettingsOpen}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              handleSettingsToggle();
            }
          }}
          sx={{
            ...SHARED_STYLES.icon.container,
            borderRadius: '4px',
            padding: spacing.xs,
            backgroundColor: isSettingsOpen ? colors.grey[100] : 'transparent',
          }}
        >
          <SettingsIcon sx={SHARED_STYLES.icon.style} />
        </Box>
        {/* Call Button */}
        <Button
          onClick={(e: React.MouseEvent<HTMLButtonElement>) => handleDialerToggle(e)} // Added type annotation for 'e'
          label="" // Icon-only button
          color={isDialerActive ? "blue" : "grey"} // Dynamic color
          prominence={isDialerActive} // Dynamic prominence
          size="small" // Adjust size as needed
          leftIcon={<AddIcCallIcon fontSize="small" />} // Pass the icon
          style={isDialerActive ? "filled" : "ghost"}
        />
        {/* Expand/Collapse Button - rightmost */}
        <Box
          onClick={handleExpandToggle}
          sx={SHARED_STYLES.icon.container}
        >
          {isExpanded ? (
            <KeyboardArrowDownIcon sx={SHARED_STYLES.icon.style} />
          ) : (
            <KeyboardArrowUpIcon sx={SHARED_STYLES.icon.style} />
          )}
        </Box>
      </Box>
      <ManualDialerOverlay
        isOpen={isDialerOpen}
        onClose={handleDialerClose} // Use dedicated close handler
        onDial={internalHandleDial} // Use the internal dial handler
        anchorEl={dialerAnchorEl}
        contacts={contacts} // Pass the configurable contacts
        onCallInitiated={handleCallInitiated} // Pass the new callback
      />
    </Box>
  ), [handleExpandToggle, isExpanded, isDialerActive, handleDialerToggle, handleDialerClose, internalHandleDial, handleCallInitiated, contacts, handleSettingsToggle, isSettingsOpen]);

  // Tabs Section: Handles tab switching UI
  const renderTabs = useCallback(() => (
    <Box sx={SHARED_STYLES.tabs.container}>
      <Tabs
        value={tabValue}
        onChange={handleTabChange}
        aria-label="call control tabs"
        sx={{
          '& .MuiTabs-flexContainer': SHARED_STYLES.tabs.flexContainer,
          '& .MuiTab-root': SHARED_STYLES.tabs.tab,
          '& .MuiTabs-indicator': {
            backgroundColor: colors.blue[600],
            height: 2,
            bottom: 2,
            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          },
        }}
      >
        <Tab
          icon={<TabLabel label="On Hold" count={onHoldCount} isSelected={tabValue === 0} />}
          iconPosition="start"
        />
        <Tab
          label={<TabLabel label="Inbound" count={inboundCount} isSelected={tabValue === 1} />}
        />
      </Tabs>
    </Box>
  ), [handleTabChange, tabValue, onHoldCount, inboundCount]);

  // Content Section: Renders either tab content or settings panel
  const renderContent = useCallback(() => {
    console.log('[WidgetV2] renderContent - isSettingsOpen:', isSettingsOpen);
    
    // If settings is open, render the CallForwardingPanel instead of tabs
    if (isSettingsOpen) {
      console.log('[WidgetV2] Rendering CallForwardingPanel');
      return (
        <Box sx={{
          flex: 1,
          overflow: "hidden",
          minHeight: 0,
          display: 'flex',
          flexDirection: 'column',
          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        }}>
          <CallForwardingPanel />
        </Box>
      );
    }

    // Otherwise render normal tab content
    const waitingCalls = queueStatus?.waitingCalls || [];

    return (
      <Box sx={{
        flex: 1,
        overflow: "hidden",
        minHeight: 0,
        display: 'flex',
        flexDirection: 'column',
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
      }}>
        <TabPanel value={tabValue} index={0}>
          {onHoldCount > 0 ? (
            heldCalls.map((call: QueuedCall) => (
              <OnHoldCallListItem
                key={call.callSid || call.situationId || Math.random()}
                call={call}
                onSwitchCall={handleSwitchCall}
              />
            ))
          ) : (
            <Typography sx={{
              ...typography.styles.body3,
              color: colors.grey[400],
              textAlign: "center",
              padding: spacing.m,
              fontFeatureSettings: "'liga' off, 'clig' off",
              textTransform: 'uppercase',
              textEdge: 'cap',
              leadingTrim: 'both'
            }}>
              NO CALLS ON HOLD
            </Typography>
          )}
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          {waitingCalls.length > 0 ? (
            waitingCalls.map((call: QueuedCall) => (
              <IncomingCallListItem
                key={call.callSid}
                call={call}
                isEmergency={call.attributes?.emergency === 'true'}
                // onAcceptCall={() => handleAcceptNextCall()}
                onAcceptCall={() => handleDequeueCall(call.callSid)}
                isAccepting={isAcceptingCall}
              />
            ))
          ) : (
            <Typography sx={{
              ...typography.styles.body3,
              color: colors.grey[400],
              textAlign: "center",
              padding: spacing.m,
              fontFeatureSettings: "'liga' off, 'clig' off",
              textTransform: 'uppercase',
              textEdge: 'cap',
              leadingTrim: 'both'
            }}>
              NO INBOUND CALLS
            </Typography>
          )}
        </TabPanel>
      </Box>
    );
  }, [isSettingsOpen, tabValue, onHoldCount, heldCalls, handleSwitchCall, queueStatus, handleAcceptNextCall, isAcceptingCall]);

  // Main Render
  return (
    <Box
      sx={{
        ...SHARED_STYLES.layout.base,
        borderTop: `1px solid ${colors.grey[300]}`,
        backgroundColor: '#FFFFFF',
        position: "relative",
        zIndex: 1,
        overflow: "hidden",
        width: '100%',
        minHeight: '48px', // Always show at least the header
        boxShadow: '0px -4px 12px 0px rgba(0, 0, 0, 0.08)',
      }}
    >
      {renderHeader()}
      <Collapse
        in={isExpanded}
        timeout={300}
        easing={{
          enter: 'cubic-bezier(0.4, 0, 0.2, 1)',
          exit: 'cubic-bezier(0.4, 0, 0.2, 1)',
        }}
        unmountOnExit={false}
        sx={{
          width: '100%',
          '& .MuiCollapse-wrapper': {
            width: '100%',
          },
          '& .MuiCollapse-wrapperInner': {
            width: '100%',
          },
        }}
      >
        <Box 
          sx={{
            display: 'flex',
            flexDirection: 'column',
            width: '100%',
            height: '387px', // 435px total - 48px header
            boxShadow: "0px 0px 20px 0px rgba(0, 0, 0, 0.10)",
            overflow: 'hidden',
          }}
        >
          {activeCall && (
            <Box sx={{ padding: spacing.m, gap: spacing.l, alignItems: 'stretch', display: 'flex', flexDirection: 'column' }}>
              <ActiveCall
                activeCall={activeCall}
                isMuted={isMuted}
                isConnecting={isConnecting}
                onEndCall={onEndCall}
                onHoldToggle={onHoldToggle}
                onToggleMute={onToggleMute}
              />
            </Box>
          )}
          {!isSettingsOpen && renderTabs()}
          {renderContent()}
        </Box>
      </Collapse>
    </Box>
  );
} 