import { useListAssets } from "@/app/apis/services/workflow/assets/hooks";
import { useUserAsset } from "@/app/contexts/User/UserAssetContext";
import { formatPhoneNumberForDisplay } from "@/app/utils/caller-identification";
import { colors } from "@/design-system/tokens/colors";
import { radius } from "@/design-system/tokens/radius";
import { spacing } from "@/design-system/tokens/spacing";
import { typography } from "@/design-system/tokens/typography";
import InfoIcon from '@mui/icons-material/Info';
import { Box, Switch, Tooltip, Typography } from "@mui/material";
import { ListAssetsRequest } from "proto/hero/assets/v2/assets_pb";
import { memo, useCallback, useMemo, useState } from "react";

interface CallForwardingPanelProps {
  // No isOpen/onClose needed - this will be controlled by parent component
}

// =============================================
// Component: CallForwardingPanel
// Purpose: Inline panel for Call Forwarding Settings (replaces tabs content)
// =============================================
export const CallForwardingPanel = memo(({}: CallForwardingPanelProps) => {
  const [isMobileDispatchEnabled, setIsMobileDispatchEnabled] = useState(false);
  const [selectedAssetId, setSelectedAssetId] = useState<string>('');
  const [isOverrideEnabled, setIsOverrideEnabled] = useState(false);
  const [selectedOverrideNumber, setSelectedOverrideNumber] = useState<string>('');

  // Get current user's organization
  const { asset: currentUserAsset } = useUserAsset();

  // Fetch all assets in the organization for the dispatcher selector
  const { data: assetsResponse } = useListAssets(
    {
      pageSize: 100, // Get all assets
      pageToken: "",
    } as ListAssetsRequest,
    {
      enabled: !!currentUserAsset?.orgId,
    }
  );

  const assets = assetsResponse?.assets || [];

  // Extract unique phone numbers from assets for the override dropdown
  const uniquePhoneNumbers = useMemo(() => {
    const phoneNumbers = assets
      .map(asset => asset.contactNo)
      .filter(phone => phone && phone.trim() !== '') // Remove empty/null numbers
      .map(phone => phone!); // TypeScript assertion since we filtered nulls

    // Remove duplicates
    return Array.from(new Set(phoneNumbers));
  }, [assets]);

  // Base dropdown styling to match design system specifications
  const baseDropdownStyle = {
    height: '40px',
    minHeight: '40px',
    padding: `${spacing.xs} ${spacing.s}`, // 8px 12px
    paddingRight: `calc(${spacing.s} + 24px + ${spacing.s})`, // 12px + icon + 12px spacing
    borderRadius: radius.m, // 8px
    border: `1px solid ${colors.grey[200]}`, // #E5E7EB
    backgroundColor: '#FFF',
    fontFeatureSettings: "'liga' off, 'clig' off",
    cursor: 'pointer',
    width: '100%',
    appearance: 'none' as const,
    backgroundImage: `url("data:image/svg+xml;charset=UTF-8,%3csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='m7 10 5 5 5-5H7Z' fill='%23101828'/%3e%3c/svg%3e")`,
    backgroundRepeat: 'no-repeat',
    backgroundPosition: `right ${spacing.s} center`, // 12px from right
    backgroundSize: '24px 24px',
  };

  // Style when option is selected (body1)
  const selectedDropdownStyle = {
    ...baseDropdownStyle,
    ...typography.styles.body1, // 16px, 500 weight, 0.15px letter-spacing
    color: colors.grey[900], // #101828
  };

  // Style when showing placeholder (body2)  
  const placeholderDropdownStyle = {
    ...baseDropdownStyle,
    ...typography.styles.body2, // 16px, 400 weight, 0.15px letter-spacing
    color: colors.grey[400], // #98A1AE
  };

  const handleToggleChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setIsMobileDispatchEnabled(event.target.checked);
    console.log('[CallForwardingPanel] Mobile dispatch toggled:', event.target.checked);
  }, []);

  const handleAssetChange = useCallback((event: any) => {
    const assetId = event.target.value;
    setSelectedAssetId(assetId);

    // Find the selected asset and automatically set the number if Override is OFF
    const selectedAsset = assets.find(asset => asset.id === assetId);
    console.log('[CallForwardingPanel] Asset changed:', selectedAsset?.name, selectedAsset?.contactNo);
  }, [assets]);

  const handleOverrideChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setIsOverrideEnabled(event.target.checked);
    console.log('[CallForwardingPanel] Override toggled:', event.target.checked);
  }, []);

  const handleOverrideNumberChange = useCallback((event: any) => {
    const phoneNumber = event.target.value;
    setSelectedOverrideNumber(phoneNumber);
    console.log('[CallForwardingPanel] Override number changed:', phoneNumber);
  }, []);

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
        width: '100%',
        backgroundColor: '#FFF',
      }}
    >
      {/* Settings Section */}
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          gap: spacing.m,
          padding: spacing.m,
          flex: 1,
        }}
      >
        {/* Settings Title with separator line */}
        <Box
          sx={{
            position: 'relative',
            paddingBottom: spacing.s,
          }}
        >
          <Typography
            sx={{
              ...typography.styles.body3,
              color: colors.grey[700],
              textAlign: 'left',
              fontFeatureSettings: "'liga' off, 'clig' off",
            }}
          >
            Settings
          </Typography>
          {/* Separator line - extends fully across */}
          <Box
            sx={{
              position: 'absolute',
              bottom: 0,
              left: `-${spacing.m}`,
              right: `-${spacing.m}`,
              height: '1px',
              backgroundColor: colors.grey[200],
            }}
          />
        </Box>

        {/* Enable Mobile Dispatch Section */}
        <Box sx={{ display: 'flex', flexDirection: 'column' }}>
          {/* Toggle Row */}
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              width: '100%',
              marginBottom: 0.5, // tighten bottom spacing
            }}
          >
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: spacing.xs,
              }}
            >
              <Typography
                sx={{
                  ...typography.styles.body4,
                  color: colors.grey[700],
                  fontFeatureSettings: "'liga' off, 'clig' off",
                  margin: 0, // nuke default margin
                }}
              >
                Enable mobile dispatch
              </Typography>
              <Tooltip
                title="Forward all calls to a designated phone number"
                placement="bottom"
                slotProps={{
                  tooltip: {
                    sx: {
                      backgroundColor: colors.grey[900],
                      color: colors.grey[50],
                      fontSize: '12px',
                      padding: '8px 12px',
                      borderRadius: '6px',
                      boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.15)',
                    }
                  },
                  arrow: {
                    sx: {
                      color: colors.grey[900],
                    }
                  }
                }}
                arrow
              >
                <InfoIcon
                  sx={{
                    fill: colors.grey[400],
                    width: '11.667px',
                    height: '11.667px',
                    flexShrink: 0,
                    cursor: 'pointer',
                  }}
                />
              </Tooltip>
            </Box>
            
            <Switch
              checked={isMobileDispatchEnabled}
              onChange={handleToggleChange}
              sx={{
                '& .MuiSwitch-switchBase.Mui-checked': {
                  color: '#fff',
                  '&:hover': {
                    backgroundColor: 'rgba(255, 255, 255, 0.08)',
                  },
                },
                '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                  backgroundColor: colors.blue[600],
                  opacity: 1,
                },
                '& .MuiSwitch-track': {
                  backgroundColor: colors.grey[300],
                  opacity: 1,
                },
                '& .MuiSwitch-thumb': {
                  color: '#fff',
                },
              }}
            />
          </Box>

          {/* Helper text tucked right under */}
          {isMobileDispatchEnabled && (
            <Typography
              sx={{
                ...typography.styles.body4,
                color: colors.grey[600],
                fontSize: '12px',
                margin: 0, // kill default
                lineHeight: 1.3, // tighter vertical rhythm
              }}
            >
              This will forward all calls to a designated phone number. To save this setting, please select from the list below and save.
            </Typography>
          )}
        </Box>

        {/* Dispatcher and Override Section - shown when enabled */}
        {isMobileDispatchEnabled && (
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              gap: spacing.xs,
              marginTop: spacing.m,
            }}
          >
            {/* Assigned Dispatcher */}
            <Typography
              sx={{
                ...typography.styles.body4,
                color: colors.grey[700],
                fontFeatureSettings: "'liga' off, 'clig' off",
              }}
            >
              Assigned Dispatcher
            </Typography>

            {/* Dispatcher Dropdown */}
            <select
              value={selectedAssetId}
              onChange={handleAssetChange}
              style={selectedAssetId ? selectedDropdownStyle : placeholderDropdownStyle}
            >
              <option value="">Assign a Dispatcher</option>
              {assets.map((asset) => {
                const displayPhone = asset.contactNo
                  ? formatPhoneNumberForDisplay(asset.contactNo)
                  : '(---) --- ----';
                return (
                  <option key={asset.id} value={asset.id}>
                    {asset.name} • {displayPhone}
                  </option>
                );
              })}
            </select>

            {/* Override Checkbox - tight spacing from dropdown above */}
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: spacing.xs,
                marginTop: spacing.xs,
              }}
            >
              <input
                type="checkbox"
                checked={isOverrideEnabled}
                onChange={handleOverrideChange}
                style={{
                  width: '16px',
                  height: '16px',
                  cursor: 'pointer',
                }}
              />
              <Typography
                sx={{
                  ...typography.styles.body4,
                  color: colors.grey[700],
                  fontFeatureSettings: "'liga' off, 'clig' off",
                }}
              >
                Override Default Number
              </Typography>
            </Box>

            {/* Phone Number Dropdown - shown when override is enabled */}
            {isOverrideEnabled && (
              <select
                value={selectedOverrideNumber}
                onChange={handleOverrideNumberChange}
                style={selectedOverrideNumber ? selectedDropdownStyle : placeholderDropdownStyle}
              >
                <option value="">Select phone number...</option>
                {uniquePhoneNumbers.map((phoneNumber) => {
                  const displayPhone = formatPhoneNumberForDisplay(phoneNumber);
                  return (
                    <option key={phoneNumber} value={phoneNumber}>
                      {displayPhone}
                    </option>
                  );
                })}
              </select>
            )}
          </Box>
        )}
      </Box>
    </Box>
  );
});

CallForwardingPanel.displayName = 'CallForwardingPanel';