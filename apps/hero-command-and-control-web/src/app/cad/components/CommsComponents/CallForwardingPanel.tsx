import { useListAssets } from "@/app/apis/services/workflow/assets/hooks";
import { useUserAsset } from "@/app/contexts/User/UserAssetContext";
import { formatPhoneNumberForDisplay } from "@/app/utils/caller-identification";
import { colors } from "@/design-system/tokens/colors";
import { radius } from "@/design-system/tokens/radius";
import { spacing } from "@/design-system/tokens/spacing";
import { typography } from "@/design-system/tokens/typography";
import InfoIcon from '@mui/icons-material/Info';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import { Box, Switch, Tooltip, Typography } from "@mui/material";
import { ListAssetsRequest } from "proto/hero/assets/v2/assets_pb";
import { memo, useCallback, useMemo, useState } from "react";

interface CallForwardingPanelProps {
  // No isOpen/onClose needed - this will be controlled by parent component
}

// =============================================
// Component: CallForwardingPanel
// Purpose: Inline panel for Call Forwarding Settings (replaces tabs content)
// =============================================
export const CallForwardingPanel = memo(({}: CallForwardingPanelProps) => {
  const [isMobileDispatchEnabled, setIsMobileDispatchEnabled] = useState(false);
  const [selectedAssetId, setSelectedAssetId] = useState<string>('');
  const [isOverrideEnabled, setIsOverrideEnabled] = useState(false);
  const [selectedOverrideNumber, setSelectedOverrideNumber] = useState<string>('');

  // Get current user's organization
  const { asset: currentUserAsset } = useUserAsset();

  // Fetch all assets in the organization for the dispatcher selector
  const { data: assetsResponse } = useListAssets(
    {
      pageSize: 100, // Get all assets
      pageToken: "",
    } as ListAssetsRequest,
    {
      enabled: !!currentUserAsset?.orgId,
    }
  );

  const assets = assetsResponse?.assets || [];

  // Extract unique phone numbers from assets for the override dropdown
  const uniquePhoneNumbers = useMemo(() => {
    const phoneNumbers = assets
      .map(asset => asset.contactNo)
      .filter(phone => phone && phone.trim() !== '') // Remove empty/null numbers
      .map(phone => phone!); // TypeScript assertion since we filtered nulls

    // Remove duplicates
    return Array.from(new Set(phoneNumbers));
  }, [assets]);

  // Dropdown container styling using design system tokens
  const dropdownContainerSx = {
    position: 'relative',
    width: '100%',
  };

  // Base dropdown styling using MUI sx prop
  const baseDropdownSx = {
    height: '40px',
    minHeight: '40px',
    padding: `${spacing.xs} ${spacing.s}`,
    paddingRight: `calc(${spacing.s} + 24px + ${spacing.s})`, // Space for icon
    borderRadius: radius.m,
    border: `1px solid ${colors.grey[200]}`,
    backgroundColor: colors.grey[50],
    fontFeatureSettings: "'liga' off, 'clig' off",
    cursor: 'pointer',
    width: '100%',
    appearance: 'none',
    '&:focus': {
      outline: 'none',
      borderColor: colors.blue[600],
    },
  };

  // Dropdown arrow icon styling
  const dropdownIconSx = {
    position: 'absolute',
    right: spacing.s,
    top: '50%',
    transform: 'translateY(-50%)',
    pointerEvents: 'none',
    color: colors.grey[700],
    fontSize: '20px',
  };

  const handleToggleChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setIsMobileDispatchEnabled(event.target.checked);
    console.log('[CallForwardingPanel] Mobile dispatch toggled:', event.target.checked);
  }, []);

  const handleAssetChange = useCallback((event: any) => {
    const assetId = event.target.value;
    setSelectedAssetId(assetId);

    // Find the selected asset and automatically set the number if Override is OFF
    const selectedAsset = assets.find(asset => asset.id === assetId);
    console.log('[CallForwardingPanel] Asset changed:', selectedAsset?.name, selectedAsset?.contactNo);
  }, [assets]);

  const handleOverrideChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setIsOverrideEnabled(event.target.checked);
    console.log('[CallForwardingPanel] Override toggled:', event.target.checked);
  }, []);

  const handleOverrideNumberChange = useCallback((event: any) => {
    const phoneNumber = event.target.value;
    setSelectedOverrideNumber(phoneNumber);
    console.log('[CallForwardingPanel] Override number changed:', phoneNumber);
  }, []);

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
        width: '100%',
        backgroundColor: colors.grey[50],
      }}
    >
      {/* Main Content Container */}
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          gap: spacing.m,
          padding: spacing.m,
          flex: 1,
        }}
      >
        {/* Settings Header Section */}
        <Box
          sx={{
            position: 'relative',
            paddingBottom: spacing.s,
          }}
        >
          <Typography
            sx={{
              ...typography.styles.body3,
              color: colors.grey[700],
              fontFeatureSettings: "'liga' off, 'clig' off",
            }}
          >
            Settings
          </Typography>
          {/* Separator line */}
          <Box
            sx={{
              position: 'absolute',
              bottom: 0,
              left: `-${spacing.m}`,
              right: `-${spacing.m}`,
              height: '1px',
              backgroundColor: colors.grey[200],
            }}
          />
        </Box>

        {/* Mobile Dispatch Toggle Section */}
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            gap: spacing.xs,
          }}
        >
          {/* Toggle Control Row */}
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              width: '100%',
            }}
          >
            {/* Label with Info Icon */}
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: spacing.xs,
              }}
            >
              <Typography
                sx={{
                  ...typography.styles.body4,
                  color: colors.grey[700],
                  fontFeatureSettings: "'liga' off, 'clig' off",
                }}
              >
                Enable mobile dispatch
              </Typography>
              <Tooltip
                title="Forward all calls to a designated phone number"
                placement="bottom"
                slotProps={{
                  tooltip: {
                    sx: {
                      backgroundColor: colors.grey[900],
                      color: colors.grey[50],
                      fontSize: '12px',
                      padding: `${spacing.xs} ${spacing.s}`,
                      borderRadius: radius.s,
                      boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.15)',
                    }
                  },
                  arrow: {
                    sx: {
                      color: colors.grey[900],
                    }
                  }
                }}
                arrow
              >
                <InfoIcon
                  sx={{
                    color: colors.grey[400],
                    fontSize: '12px',
                    cursor: 'pointer',
                  }}
                />
              </Tooltip>
            </Box>

            {/* Switch Control */}
            <Switch
              checked={isMobileDispatchEnabled}
              onChange={handleToggleChange}
              sx={{
                '& .MuiSwitch-switchBase.Mui-checked': {
                  color: colors.grey[50],
                  '&:hover': {
                    backgroundColor: 'rgba(255, 255, 255, 0.08)',
                  },
                },
                '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                  backgroundColor: colors.blue[600],
                  opacity: 1,
                },
                '& .MuiSwitch-track': {
                  backgroundColor: colors.grey[300],
                  opacity: 1,
                },
                '& .MuiSwitch-thumb': {
                  color: colors.grey[50],
                },
              }}
            />
          </Box>

          {/* Helper Text */}
          {isMobileDispatchEnabled && (
            <Typography
              sx={{
                ...typography.styles.body4,
                color: colors.grey[600],
                fontSize: '12px',
                lineHeight: 1.3,
              }}
            >
              This will forward all calls to a designated phone number. To save this setting, please select from the list below and save.
            </Typography>
          )}
        </Box>

        {/* Dispatcher Configuration Section */}
        {isMobileDispatchEnabled && (
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              gap: spacing.m,
              marginTop: spacing.l,
            }}
          >
            {/* Dispatcher Selection */}
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                gap: spacing.xs,
              }}
            >
              <Typography
                sx={{
                  ...typography.styles.body4,
                  color: colors.grey[700],
                  fontFeatureSettings: "'liga' off, 'clig' off",
                }}
              >
                Assigned Dispatcher
              </Typography>

              <Box sx={dropdownContainerSx}>
                <Box
                  component="select"
                  value={selectedAssetId}
                  onChange={handleAssetChange}
                  sx={{
                    ...baseDropdownSx,
                    ...typography.styles[selectedAssetId ? 'body1' : 'body2'],
                    color: selectedAssetId ? colors.grey[900] : colors.grey[400],
                  }}
                >
                  <option value="">Assign a Dispatcher</option>
                  {assets.map((asset) => {
                    const displayPhone = asset.contactNo
                      ? formatPhoneNumberForDisplay(asset.contactNo)
                      : '(---) --- ----';
                    return (
                      <option key={asset.id} value={asset.id}>
                        {asset.name} • {displayPhone}
                      </option>
                    );
                  })}
                </Box>
                <KeyboardArrowDownIcon sx={dropdownIconSx} />
              </Box>
            </Box>

            {/* Override Number Section */}
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                gap: spacing.xs,
              }}
            >
              {/* Override Checkbox */}
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: spacing.xs,
                }}
              >
                <Box
                  component="input"
                  type="checkbox"
                  checked={isOverrideEnabled}
                  onChange={handleOverrideChange}
                  sx={{
                    width: '16px',
                    height: '16px',
                    cursor: 'pointer',
                    accentColor: colors.blue[600],
                  }}
                />
                <Typography
                  sx={{
                    ...typography.styles.body4,
                    color: colors.grey[700],
                    fontFeatureSettings: "'liga' off, 'clig' off",
                  }}
                >
                  Override Default Number
                </Typography>
              </Box>

              {/* Override Phone Number Dropdown */}
              {isOverrideEnabled && (
                <Box sx={dropdownContainerSx}>
                  <Box
                    component="select"
                    value={selectedOverrideNumber}
                    onChange={handleOverrideNumberChange}
                    sx={{
                      ...baseDropdownSx,
                      ...typography.styles[selectedOverrideNumber ? 'body1' : 'body2'],
                      color: selectedOverrideNumber ? colors.grey[900] : colors.grey[400],
                    }}
                  >
                    <option value="">Select phone number...</option>
                    {uniquePhoneNumbers.map((phoneNumber) => {
                      const displayPhone = formatPhoneNumberForDisplay(phoneNumber);
                      return (
                        <option key={phoneNumber} value={phoneNumber}>
                          {displayPhone}
                        </option>
                      );
                    })}
                  </Box>
                  <KeyboardArrowDownIcon sx={dropdownIconSx} />
                </Box>
              )}
            </Box>
          </Box>
        )}
      </Box>
    </Box>
  );
});

CallForwardingPanel.displayName = 'CallForwardingPanel';